/**
 * DLMM串联池流动性管理脚本 - 服务功能
 *
 * 整合所有核心业务逻辑，包括连接管理、钱包服务、池子发现、价格监控和流动性调整
 */
import { Connection, Keypair, PublicKey, Transaction } from '@solana/web3.js';
import { Pool, PoolChain } from './models';
/**
 * 连接服务
 * 管理与Solana区块链的连接
 */
export declare class ConnectionService {
    private connection;
    private isConnected;
    /**
     * 构造函数
     */
    constructor();
    /**
     * 初始化连接
     */
    initialize(): Promise<void>;
    /**
     * 检查连接是否正常
     */
    private checkConnection;
    /**
     * 确保连接可用
     */
    ensureConnected(): Promise<void>;
    /**
     * 获取连接实例
     */
    getConnection(): Connection;
}
/**
 * 钱包服务
 * 管理用户钱包和交易签名
 */
export declare class WalletService {
    private wallet;
    private connectionService;
    /**
     * 构造函数
     */
    constructor(connectionService: ConnectionService);
    /**
     * 初始化钱包
     * 如果存在加密的私钥文件，则从加密文件加载私钥
     * 否则从配置文件加载私钥
     */
    init(): Promise<void>;
    /**
     * 从配置文件加载私钥
     */
    private loadPrivateKeyFromConfig;
    /**
     * 获取钱包公钥
     */
    getPublicKey(): PublicKey;
    /**
     * 获取钱包密钥对
     */
    getKeypair(): Keypair;
    /**
     * 获取钱包地址字符串
     */
    getAddress(): string;
    /**
     * 获取SOL余额
     */
    getSolBalance(): Promise<number>;
    /**
     * 添加优先级费用到交易
     */
    addPriorityFee(transaction: Transaction): Transaction;
    /**
     * 签名并发送交易
     */
    signAndSendTransaction(transaction: Transaction): Promise<string>;
}
/**
 * 池子发现服务
 * 发现用户创建的所有池子
 */
export declare class PoolDiscoveryService {
    private connectionService;
    private walletService;
    private poolChain;
    /**
     * 构造函数
     */
    constructor(connectionService: ConnectionService, walletService: WalletService);
    /**
     * 发现并加载所有池子
     */
    discoverPools(): Promise<PoolChain>;
    /**
     * 记录池子摘要信息
     */
    private logPoolSummary;
    /**
     * 更新池子显示
     */
    updatePoolsDisplay(): void;
    /**
     * 获取池子链
     */
    getPoolChain(): PoolChain;
}
/**
 * 价格监控服务
 * 监控价格变化并触发相应事件
 */
export declare class PriceMonitorService {
    private connectionService;
    private poolChain;
    private currentPrice;
    private currentBinId;
    private previousBinId;
    private monitoring;
    private onPriceChangeCallbacks;
    private onPoolCrossingCallbacks;
    /**
     * 构造函数
     */
    constructor(connectionService: ConnectionService, poolChain: PoolChain);
    /**
     * 获取当前价格
     */
    getCurrentPrice(): Promise<number>;
    /**
     * 获取当前活跃bin ID
     */
    getCurrentBinId(): number;
    /**
     * 更新价格和活跃bin
     */
    private updatePrice;
    /**
     * 处理bin变化并显示
     */
    private processBinChange;
    /**
     * 根据bin ID查找所在的池子
     */
    private findPoolByBin;
    /**
     * 触发价格变化回调
     */
    private triggerPriceChangeCallbacks;
    /**
     * 触发池子跨越回调
     */
    private triggerPoolCrossingCallbacks;
    /**
     * 注册价格变化回调
     */
    onPriceChange(callback: (price: number, previousPrice: number) => void): void;
    /**
     * 注册池子跨越回调
     */
    onPoolCrossing(callback: (previousPool: Pool | undefined, currentPool: Pool | undefined) => void): void;
    /**
     * 开始监控
     */
    startMonitoring(): Promise<void>;
    /**
     * 监控循环
     */
    private monitoringLoop;
    /**
     * 停止监控
     */
    stopMonitoring(): void;
}
/**
 * 流动性调整服务
 * 执行流动性移除和添加操作
 */
export declare class LiquidityAdjustmentService {
    private connectionService;
    private walletService;
    private poolChain;
    private priceMonitorService;
    private adjusting;
    private poolDiscoveryService;
    /**
     * 构造函数
     */
    constructor(connectionService: ConnectionService, walletService: WalletService, poolChain: PoolChain, priceMonitorService: PriceMonitorService, poolDiscoveryService: PoolDiscoveryService);
    /**
     * 处理池子跨越事件
     */
    private handlePoolCrossing;
    /**
     * 检查并调整相邻池子
     */
    checkAndAdjustNeighboringPools(): Promise<void>;
    /**
     * 调整单个头寸
     */
    private adjustPosition;
    /**
     * 移除单个头寸的流动性
     */
    private removePositionLiquidity;
    /**
     * 为单个头寸按策略添加流动性
     */
    private addLiquidityForPosition;
}
