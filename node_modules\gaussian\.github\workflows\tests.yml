name: tests

on: push

env:
  CI: true

jobs:
  tests:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version:
          - 10.x # maintainence ends 2021-04-30
          - 11.x # deprecated
          - 12.x # maintainence ends 2022-04-30
          - 13.x # deprecated
          - 14.x # maintainence ends 2023-04-30
          - 15.x
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: ${{ matrix.node-version }}
      - run: npm install
      - run: npm test

  coverage:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: 14.x
      - run: npm install
      - run: npm run build --if-present
      - run: npm run test:coverage
      - name: Coveralls
        uses: coverallsapp/github-action@master
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
