/**
 * DLMM串联池流动性管理脚本 - 工具函数
 *
 * 提供通用辅助功能，包括重试机制、数据处理和BidAsk模型验证
 */
/**
 * 带重试的异步函数执行
 * @param fn 要执行的异步函数
 * @param context 上下文描述(用于日志)
 * @param maxRetries 最大重试次数
 * @param initialDelay 初始延迟(毫秒)
 * @param backoffFactor 退避因子
 * @returns 异步函数结果
 */
export declare function withRetry<T>(fn: () => Promise<T>, context: string, maxRetries?: number, initialDelay?: number, backoffFactor?: number): Promise<T>;
/**
 * 延迟指定时间
 * @param ms 延迟毫秒数
 * @returns Promise
 */
export declare function sleep(ms: number): Promise<void>;
/**
 * 格式化代币数量为可读字符串
 * @param amount 代币数量(bigint或string)
 * @param decimals 小数位数
 * @returns 格式化后的数量字符串
 */
export declare function formatAmount(amount: bigint | string | number, decimals: number): string;
/**
 * 将格式化的数量转换回原始值
 * @param formattedAmount 格式化的数量
 * @param decimals 小数位数
 * @returns 原始值
 */
export declare function parseAmount(formattedAmount: string, decimals: number): bigint;
/**
 * 计算真实价格
 * 由于不同代币精度不同，需要调整价格
 * @param price 原始价格
 * @param tokenXDecimals X代币精度(如SOL的9)
 * @param tokenYDecimals Y代币精度(如USDC的6)
 * @returns 调整后的真实价格
 */
export declare function calculateRealPrice(price: number | string, tokenXDecimals: number, tokenYDecimals: number): number;
/**
 * 格式化价格显示
 * @param price 原始价格
 * @param tokenXDecimals X代币精度
 * @param tokenYDecimals Y代币精度
 * @returns 格式化后的价格字符串
 */
export declare function formatPrice(price: number | string, tokenXDecimals: number, tokenYDecimals: number): string;
/**
 * 截断地址字符串以便显示
 * @param address 完整地址
 * @param length 截断后的长度
 * @returns 截断后的地址
 */
export declare function truncateAddress(address: string, length?: number): string;
/**
 * 验证BidAsk模型
 * 检查一系列资金分配是否符合线性增长的BidAsk模型
 *
 * @param values 资金数量数组
 * @param isAscending 资金应该是升序还是降序
 * @returns 是否符合BidAsk模型
 */
export declare function validateBidAskModel(values: number[], isAscending: boolean): boolean;
/**
 * 计算BidAsk模型的理想分布
 *
 * @param total 总资金量
 * @param binCount bin数量
 * @param isAscending 是否为升序分布
 * @returns 每个bin的资金分配数组
 */
export declare function calculateBidAskDistribution(total: number, binCount: number, isAscending: boolean): number[];
