/**
 * 私钥加密解密工具
 * 使用AES-256-GCM算法加密私钥
 */
export declare const ENCRYPTED_KEY_FILENAME = ".encrypted_wallet.dat";
export declare const DEFAULT_ENCRYPTION_PATH: string;
/**
 * 从用户终端获取密码
 */
export declare function getPasswordFromUser(prompt?: string): Promise<string>;
/**
 * 加密私钥
 * @param privateKey 要加密的私钥
 * @param password 加密密码
 * @returns 加密数据对象
 */
export declare function encryptPrivateKey(privateKey: string, password: string): {
    encryptedData: string;
    iv: string;
    salt: string;
};
/**
 * 解密私钥
 * @param encryptedData 加密数据
 * @param iv 初始化向量
 * @param salt 盐值
 * @param password 解密密码
 * @returns 解密后的私钥
 */
export declare function decryptPrivateKey(encryptedData: string, iv: string, salt: string, password: string): string;
/**
 * 保存加密的私钥到文件
 * @param encryptedData 加密数据对象
 * @param filePath 文件路径
 */
export declare function saveEncryptedKey(encryptedData: {
    encryptedData: string;
    iv: string;
    salt: string;
}, filePath?: string): void;
/**
 * 从文件加载加密的私钥
 * @param filePath 文件路径
 * @returns 加密数据对象
 */
export declare function loadEncryptedKey(filePath?: string): {
    encryptedData: string;
    iv: string;
    salt: string;
};
/**
 * 加载并解密私钥
 * @param password 解密密码
 * @param filePath 加密私钥文件路径
 * @returns 解密后的私钥
 */
export declare function loadAndDecryptPrivateKey(password?: string, filePath?: string): Promise<string>;
/**
 * 获取加密密钥文件的完整路径
 * @param customPath 自定义路径
 * @returns 完整文件路径
 */
export declare function getEncryptedKeyPath(customPath?: string): string;
