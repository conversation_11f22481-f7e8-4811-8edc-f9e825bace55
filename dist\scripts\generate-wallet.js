"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const web3_js_1 = require("@solana/web3.js");
const bs58 = __importStar(require("bs58"));
/**
 * 生成新的 Solana 钱包
 */
function generateWallet() {
    console.log('========================================');
    console.log('Solana 钱包生成器');
    console.log('========================================');
    // 生成新的密钥对
    const keypair = web3_js_1.Keypair.generate();
    // 获取公钥（钱包地址）
    const publicKey = keypair.publicKey.toString();
    // 获取私钥（Base58 格式）
    const privateKey = bs58.encode(keypair.secretKey);
    console.log('\n✅ 新钱包生成成功！');
    console.log('\n📍 钱包地址（公钥）:');
    console.log(publicKey);
    console.log('\n🔑 私钥（请妥善保管）:');
    console.log(privateKey);
    console.log('\n⚠️  重要提示:');
    console.log('1. 请将私钥保存在安全的地方');
    console.log('2. 不要与任何人分享您的私钥');
    console.log('3. 这是一个新钱包，余额为 0 SOL');
    console.log('4. 如需使用，请先向此地址转入一些 SOL');
    console.log('\n📋 接下来的步骤:');
    console.log('1. 复制上面的私钥');
    console.log('2. 运行: npm run encrypt-key');
    console.log('3. 粘贴私钥并设置密码');
    console.log('4. 启动项目: npm start');
    console.log('\n========================================');
}
// 运行生成器
generateWallet();
//# sourceMappingURL=generate-wallet.js.map