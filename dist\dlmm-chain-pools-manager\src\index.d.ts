/**
 * DLMM串联池流动性管理脚本 - 主程序入口
 *
 * 程序入口点，负责初始化各服务、实现主工作流程，以及处理错误和退出逻辑
 */
import { ConnectionService, WalletService } from './services';
/**
 * 应用主类
 */
export declare class DLMMChainPoolsManager {
    private connectionService;
    private walletService;
    private poolDiscoveryService;
    private priceMonitorService;
    private liquidityAdjustmentService;
    private running;
    /**
     * 构造函数
     */
    constructor(connectionService: ConnectionService, walletService: WalletService, displayService: any);
    /**
     * 初始化应用
     */
    initialize(): Promise<void>;
    /**
     * 启动应用
     */
    start(): Promise<void>;
    /**
     * 启动定时检查循环
     */
    private startPeriodicChecks;
    /**
     * 停止应用
     */
    stop(): void;
}
