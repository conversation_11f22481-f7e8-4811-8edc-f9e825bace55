# DLMM串联池流动性管理系统

## 项目概述

DLMM串联池流动性管理系统是一个专为Solana区块链上的DLMM（Discrete Liquidity Market Maker）池子设计的自动化管理工具。该系统能够实时监控并维护多个串联池子的流动性分布，确保流动性按照BidAsk模型正确分布，从而优化交易效率和减少滑点。

当市场价格变动，跨越不同头寸范围时，系统会自动检测并调整不符合BidAsk模型的头寸，确保市场流动性始终保持最优状态。这种自动化管理大大降低了流动性提供者的操作负担，同时提高了市场的整体效率。

## 核心功能

### 1. 动态池子管理

- **自动发现池子**：系统启动时会自动扫描并识别用户的所有DLMM池子头寸，无需手动配置
- **多池子串联管理**：支持同时管理多个串联的DLMM流动性池，确保它们协同工作
- **头寸数据实时分析**：持续收集和分析每个头寸的bin数据和流动性分布

### 2. BidAsk模型合规性检查

- **流动性分布验证**：检查每个头寸的流动性分布是否符合BidAsk模型要求
- **升序/降序分布检测**：高于当前价格的区域应为升序分布，低于当前价格的区域应为降序分布
- **合规性报告**：生成详细的头寸合规性报告，显示不符合要求的具体原因

### 3. 自动调整策略

- **流动性移除**：对不符合BidAsk模型的头寸自动移除100%流动性
- **重新添加流动性**：使用正确的BidAsk策略重新添加流动性，确保符合模型要求
- **渐进式调整**：系统会一次调整一个头寸，确保调整过程平稳进行

### 4. 实时价格监控

- **活跃Bin监控**：持续监控每个池子的活跃Bin变化
- **价格变动检测**：实时检测X/Y代币价格变动
- **池子交叉检测**：当价格跨越不同头寸范围时自动触发检查

### 5. 安全与私钥管理

- **私钥加密存储**：支持使用密码加密私钥，提高系统安全性
- **多级私钥加载**：支持从加密文件或配置文件加载私钥
- **用户身份验证**：在操作关键功能前进行身份验证

### 6. 交易优化

- **优先级费用设置**：支持设置交易优先级费用，提高交易成功率
- **交易自动重试**：遇到临时错误时自动重试，最多尝试5次
- **计算单元优化**：可配置计算单元限制，优化交易执行

### 7. 可视化与日志

- **终端实时显示**：在终端实时显示池子状态和调整过程
- **详细日志记录**：记录系统的每一步操作，便于问题排查
- **状态消息更新**：直观显示当前系统状态和操作进度

## 系统架构

DLMM串联池流动性管理系统采用简化的模块化架构设计，将系统分为以下几个主要部分：

```
dlmm-chain-pools-manager/
├── src/
│   ├── models.ts         # 数据模型定义
│   ├── services.ts       # 核心服务功能
│   ├── utils.ts          # 工具函数
│   ├── logger.ts         # 日志模块
│   ├── display.ts        # 显示模块
│   ├── config.ts         # 配置文件
│   └── index.ts          # 主程序入口
├── package.json          # 项目依赖
└── tsconfig.json         # TypeScript配置
```

### 核心模块说明

1. **数据模型 (models.ts)**：
   - 定义系统中的核心数据结构，包括池子、头寸和池子链等
   - 包含数据处理和验证逻辑
   - 实现BidAsk模型合规性检查的基础函数

2. **服务功能 (services.ts)**：
   - 整合所有核心业务逻辑到一个文件中
   - 包含连接管理、钱包服务、池子发现和价格监控功能
   - 实现流动性调整的关键算法
   - 处理与区块链交互的全部逻辑

3. **工具函数 (utils.ts)**：
   - 提供通用辅助功能和算法
   - 实现错误重试、数据格式化等功能
   - 包含通用的时间和计算工具

4. **日志模块 (logger.ts)**：
   - 管理全系统的日志记录
   - 支持多级别日志（DEBUG, INFO, WARNING, ERROR）
   - 提供结构化和格式化的日志输出

5. **显示模块 (display.ts)**：
   - 负责终端界面显示和用户交互
   - 实现池子状态和操作进度的可视化
   - 提供彩色编码的状态提示

6. **配置文件 (config.ts)**：
   - 集中管理所有系统参数和设置
   - 包含网络连接、监控和调整策略的配置项

7. **主程序 (index.ts)**：
   - 系统入口点
   - 协调各模块工作并实现主要工作流程
   - 处理启动、循环执行和优雅退出

## 安装指南

### 前置要求

- Node.js v14.0.0 或更高版本
- npm v6.0.0 或更高版本
- 一个有效的Solana钱包（用于管理流动性）

### 安装步骤

1. **克隆项目**

```bash
git clone https://github.com/yourusername/dlmm-chain-pools-manager.git
cd dlmm-chain-pools-manager
```

2. **安装依赖**

```bash
npm install
```

3. **配置系统**

编辑 `src/config.ts` 文件，设置关键参数：

```typescript
// 基本配置
export const CONFIG = {
  // Solana RPC端点URL
  RPC_ENDPOINT: "https://api.mainnet-beta.solana.com",
  // 刷新间隔（毫秒）
  REFRESH_INTERVAL_MS: 10000,
  // 是否在终端显示界面
  DISPLAY_ENABLED: true,
  // 日志级别: 'debug' | 'info' | 'warn' | 'error'
  LOG_LEVEL: "info",
};

// 钱包配置
export const WALLET_CONFIG = {
  // 私钥（Base58编码）- 生产环境建议使用环境变量或加密文件
  PRIVATE_KEY: "你的私钥",
};

// 交易配置
export const TRANSACTION_CONFIG = {
  // 是否启用优先级费用
  ENABLE_PRIORITY_FEE: true,
  // 优先级费用(microLamports)
  PRIORITY_FEE_MICROLAMPORTS: 200000,
};
```

4. **编译项目**

```bash
npm run build
```

## 使用方法

### 开发模式

开发模式下，系统会实时编译并运行：

```bash
npm run dev
```

### 生产模式

在生产环境中，先构建项目然后运行：

```bash
npm run build
npm start
```

### 退出程序

按 `Ctrl+C` 安全退出程序。

## 工作流程

系统遵循以下工作流程：

1. **初始化**：
   - 加载配置文件
   - 连接到Solana网络
   - 初始化钱包
   - 扫描并发现用户的所有DLMM头寸

2. **监控**：
   - 定期轮询活跃Bin和价格变动
   - 当检测到活跃Bin变化时，触发池子交叉检测
   - 检查相邻头寸是否符合BidAsk模型要求

3. **调整**：
   - 对不符合BidAsk模型的头寸，移除其全部流动性
   - 根据BidAsk模型重新添加流动性
   - 刷新头寸数据，确保调整生效

4. **显示**：
   - 实时更新终端显示，展示当前状态
   - 记录详细日志，以便后续分析

## 配置项说明

### 主要配置参数

| 配置项                     | 说明                       | 默认值                            |
|----------------------------|----------------------------|-----------------------------------|
| RPC_ENDPOINT               | Solana网络RPC端点          | https://api.mainnet-beta.solana.com |
| REFRESH_INTERVAL_MS        | 数据刷新间隔（毫秒）       | 10000                             |
| DISPLAY_ENABLED            | 是否启用终端显示界面       | true                              |
| LOG_LEVEL                  | 日志级别                   | info                              |
| PRIORITY_FEE_MICROLAMPORTS | 优先级费用（microLamports）| 200000                            |
| MAX_RETRIES                | 交易最大重试次数           | 5                                 |

## BidAsk模型说明

BidAsk模型是一种优化流动性分布的策略，核心理念是：

1. **高于当前价格（Ask侧）**：流动性呈**升序**分布
   - 例：bin 100有10个代币，bin 101有20个代币，bin 102有30个代币...

2. **低于当前价格（Bid侧）**：流动性呈**降序**分布
   - 例：bin 99有30个代币，bin 98有20个代币，bin 97有10个代币...

这种分布方式确保流动性集中在最可能被交易的价格区间，提高资本效率。

## 私钥加密

为了提高系统安全性，本项目支持对私钥进行加密存储，避免在配置文件中明文保存私钥。

### 加密私钥步骤

1. **运行加密命令**

```bash
npm run encrypt-key
```

2. **按提示输入私钥和密码**

```
请输入需要加密的私钥 (Base58格式): <输入你的私钥>
请设置加密密码: <输入密码>
请再次输入密码确认: <再次输入密码>
```

3. **确认加密文件创建成功**

成功后，系统会创建一个加密的私钥文件，默认保存在项目根目录的 `.key` 文件中。同时，系统会自动修改 `config.ts` 文件中的相关配置：

```typescript
export const WALLET_CONFIG = {
  USE_ENCRYPTED_KEY: true,
  // 私钥字段将被清空
  PRIVATE_KEY: "",
  // 可选：自定义加密文件路径
  KEY_FILE_PATH: "./.key"
};
```

### 使用加密私钥

启动程序时，如果配置中启用了加密私钥（`USE_ENCRYPTED_KEY: true`），系统会提示输入密码：

```bash
npm start
# 系统会提示：请输入密码解锁私钥:
```

输入正确密码后，系统会解密私钥并继续启动流程。

### 安全注意事项

- 密码应当足够复杂，包含大小写字母、数字和特殊符号
- 定期更换密码和私钥
- 加密文件(.key)包含敏感信息，请勿分享或提交到版本控制系统
- 建议将加密文件添加到 `.gitignore` 中

## 故障排除

### 常见问题解决方案

1. **连接错误**
   - 检查RPC_ENDPOINT配置是否正确
   - 确认网络连接正常
   - 尝试使用备用RPC端点

2. **交易失败**
   - 增加优先级费用值
   - 检查钱包余额是否足够
   - 查看日志了解详细错误信息

3. **配置加载错误**
   - 确认config.ts文件存在且格式正确
   - 检查文件权限

### 日志分析

系统日志包含详细的操作记录和错误信息，可帮助诊断问题：

- `[INFO]` - 一般信息，正常操作
- `[WARN]` - 警告信息，可能需要注意
- `[ERROR]` - 错误信息，需要排查
- `[DEBUG]` - 调试信息，包含详细内部操作

## 安全建议

1. **私钥安全**
   - 不要在配置文件中存储明文私钥
   - 考虑使用环境变量存储私钥
   - 定期更换私钥

2. **权限控制**
   - 为流动性管理创建专用钱包
   - 不要在该钱包中存储大量资金

3. **网络安全**
   - 使用可靠的RPC端点
   - 避免在不安全的网络上运行程序

## 许可证

本项目采用 MIT 许可证

---

感谢使用DLMM串联池流动性管理系统！ 