{"version": 3, "file": "instructions.js", "sourceRoot": "", "sources": ["../../../../src/extensions/groupMemberPointer/instructions.ts"], "names": [], "mappings": ";;;AAqCA,sGAuBC;AAaD,8FAwBC;AAjGD,yDAAmD;AACnD,qEAAwD;AAExD,6CAAoE;AACpE,qDAAsF;AACtF,+CAAmE;AACnE,0DAA+D;AAC/D,gEAA4D;AAE5D,IAAY,6BAGX;AAHD,WAAY,6BAA6B;IACrC,6FAAc,CAAA;IACd,qFAAU,CAAA;AACd,CAAC,EAHW,6BAA6B,6CAA7B,6BAA6B,QAGxC;AAEY,QAAA,gCAAgC,GAAG,IAAA,sBAAM,EAKnD;IACC,kBAAkB;IAClB,IAAA,kBAAE,EAAC,aAAa,CAAC;IACjB,IAAA,kBAAE,EAAC,+BAA+B,CAAC;IACnC,IAAA,+BAAS,EAAC,WAAW,CAAC;IACtB,IAAA,+BAAS,EAAC,eAAe,CAAC;CAC7B,CAAC,CAAC;AAEH;;;;;;;;;GASG;AACH,SAAgB,6CAA6C,CACzD,IAAe,EACf,SAA2B,EAC3B,aAA+B,EAC/B,YAAuB,oCAAqB;IAE5C,IAAI,CAAC,IAAA,wCAAyB,EAAC,SAAS,CAAC,EAAE,CAAC;QACxC,MAAM,IAAI,4CAAgC,EAAE,CAAC;IACjD,CAAC;IACD,MAAM,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;IAEnE,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,wCAAgC,CAAC,IAAI,CAAC,CAAC;IACjE,wCAAgC,CAAC,MAAM,CACnC;QACI,WAAW,EAAE,2BAAgB,CAAC,2BAA2B;QACzD,6BAA6B,EAAE,6BAA6B,CAAC,UAAU;QACvE,SAAS,EAAE,SAAS,aAAT,SAAS,cAAT,SAAS,GAAI,mBAAS,CAAC,OAAO;QACzC,aAAa,EAAE,aAAa,aAAb,aAAa,cAAb,aAAa,GAAI,mBAAS,CAAC,OAAO;KACpD,EACD,IAAI,CACP,CAAC;IAEF,OAAO,IAAI,gCAAsB,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AACvE,CAAC;AAEY,QAAA,4BAA4B,GAAG,IAAA,sBAAM,EAI/C;IACC,kBAAkB;IAClB,IAAA,kBAAE,EAAC,aAAa,CAAC;IACjB,IAAA,kBAAE,EAAC,+BAA+B,CAAC;IACnC,IAAA,+BAAS,EAAC,eAAe,CAAC;CAC7B,CAAC,CAAC;AAEH,SAAgB,yCAAyC,CACrD,IAAe,EACf,SAAoB,EACpB,aAA+B,EAC/B,eAAuC,EAAE,EACzC,YAAuB,oCAAqB;IAE5C,IAAI,CAAC,IAAA,wCAAyB,EAAC,SAAS,CAAC,EAAE,CAAC;QACxC,MAAM,IAAI,4CAAgC,EAAE,CAAC;IACjD,CAAC;IAED,MAAM,IAAI,GAAG,IAAA,wBAAU,EAAC,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;IAExG,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,oCAA4B,CAAC,IAAI,CAAC,CAAC;IAC7D,oCAA4B,CAAC,MAAM,CAC/B;QACI,WAAW,EAAE,2BAAgB,CAAC,2BAA2B;QACzD,6BAA6B,EAAE,6BAA6B,CAAC,MAAM;QACnE,aAAa,EAAE,aAAa,aAAb,aAAa,cAAb,aAAa,GAAI,mBAAS,CAAC,OAAO;KACpD,EACD,IAAI,CACP,CAAC;IAEF,OAAO,IAAI,gCAAsB,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AACvE,CAAC"}