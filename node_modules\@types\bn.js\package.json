{"name": "@types/bn.js", "version": "5.1.6", "description": "TypeScript definitions for bn.js", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/bn.js", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "LogvinovLeon", "url": "https://github.com/LogvinovLeon"}, {"name": "<PERSON>", "githubUsername": "HenryNguyen5", "url": "https://github.com/HenryNguyen5"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/Gilthoniel"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/bn.js"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "77c4b14cadc84bb0d9111c14554e970a2fe4b9f4ff93d5e0e5d873b67482601e", "typeScriptVersion": "4.8"}