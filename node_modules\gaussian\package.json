{"name": "gaussian", "description": "A JavaScript model of a Gaussian distribution", "version": "1.3.0", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/errcw/gaussian", "repository": {"type": "git", "url": "git://github.com/errcw/gaussian.git"}, "license": "MIT", "main": "lib/gaussian", "scripts": {"test": "jest", "test:coverage": "jest --coverage"}, "engines": {"node": ">= 0.6.0"}, "keywords": ["gaussian", "normal", "distribution"], "devDependencies": {"jest": "26.6.3"}, "prettier": {"semi": true, "singleQuote": true, "tabWidth": 2}}