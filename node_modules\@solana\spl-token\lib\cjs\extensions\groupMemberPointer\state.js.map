{"version": 3, "file": "state.js", "sourceRoot": "", "sources": ["../../../../src/extensions/groupMemberPointer/state.ts"], "names": [], "mappings": ";;;AAsBA,gEAaC;AAnCD,yDAA+C;AAC/C,qEAAwD;AACxD,6CAA4C;AAE5C,0DAAsE;AAUtE,iEAAiE;AACpD,QAAA,wBAAwB,GAAG,IAAA,sBAAM,EAAqD;IAC/F,IAAA,+BAAS,EAAC,WAAW,CAAC;IACtB,IAAA,+BAAS,EAAC,eAAe,CAAC;CAC7B,CAAC,CAAC;AAEU,QAAA,yBAAyB,GAAG,gCAAwB,CAAC,IAAI,CAAC;AAEvE,SAAgB,0BAA0B,CAAC,IAAU;IACjD,MAAM,aAAa,GAAG,IAAA,mCAAgB,EAAC,gCAAa,CAAC,kBAAkB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IACvF,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;QACzB,MAAM,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG,gCAAwB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAEpF,wCAAwC;QACxC,OAAO;YACH,SAAS,EAAE,SAAS,CAAC,MAAM,CAAC,mBAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;YACjE,aAAa,EAAE,aAAa,CAAC,MAAM,CAAC,mBAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa;SAChF,CAAC;IACN,CAAC;SAAM,CAAC;QACJ,OAAO,IAAI,CAAC;IAChB,CAAC;AACL,CAAC"}