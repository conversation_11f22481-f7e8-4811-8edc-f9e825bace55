/**
 * DLMM串联池流动性管理脚本 - 配置文件
 *
 * 本文件集中管理所有配置参数，方便统一调整和维护
 */
export declare const APPLICATION_CONFIG: {
    VERSION: string;
    NAME: string;
};
export declare const CONNECTION_CONFIG: {
    RPC_ENDPOINT: string;
    BACKUP_RPC_ENDPOINTS: string[];
    CONNECTION_OPTIONS: {
        commitment: string;
        disableRetryOnRateLimit: boolean;
        confirmTransactionInitialTimeout: number;
    };
    MAX_CONNECTION_RETRIES: number;
    RECONNECT_BASE_DELAY_MS: number;
    BALANCE_CHECK_INTERVAL_SEC: number;
    MIN_SOL_BALANCE: number;
};
export declare const DLMM_CONFIG: {
    POOL_ADDRESSES: string[];
    MAX_POOL_FETCH_RETRIES: number;
    MAX_POSITION_FETCH_RETRIES: number;
    MAX_POOLS_TO_CHECK: number;
};
export declare const WALLET_CONFIG: {
    PRIVATE_KEY: string;
};
export declare const MONITOR_CONFIG: {
    PRICE_CHECK_INTERVAL_MS: number;
};
export declare const ADJUSTMENT_CONFIG: {
    MAX_ADJUSTMENT_RETRIES: number;
    MIN_LIQUIDITY_RATIO: number;
};
export declare const ERROR_CONFIG: {
    RETRY_CONFIG: {
        MAX_RETRIES: number;
        INITIAL_RETRY_DELAY_MS: number;
        BACKOFF_FACTOR: number;
    };
};
export declare const TRANSACTION_CONFIG: {
    ENABLE_PRIORITY_FEE: boolean;
    PRIORITY_FEE_MICROLAMPORTS: number;
    COMPUTE_UNIT_LIMIT: number;
    AUTO_COMPUTE_UNIT_LIMIT: boolean;
    TRANSACTION_RETRY: {
        MAX_RETRIES: number;
        RETRY_INTERVAL_MS: number;
        TRANSACTION_TIMEOUT_MS: number;
    };
};
export declare const LOGGER_CONFIG: {
    LOG_LEVEL: string;
    TIMESTAMP: boolean;
    LOG_TO_FILE: boolean;
    LOG_FILE_PATH: string;
};
export declare const DISPLAY_CONFIG: {
    TABLE_COLUMN_WIDTHS: {
        POOL_ADDRESS: number;
        BIN_RANGE: number;
        PRICE_RANGE: number;
        TOKEN_X: number;
        TOKEN_Y: number;
        STATUS: number;
        POSITION_ID: number;
    };
    USE_COLORS: boolean;
    REFRESH_RATE_MS: number;
};
