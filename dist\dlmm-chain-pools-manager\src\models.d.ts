/**
 * DLMM串联池流动性管理脚本 - 数据模型
 *
 * 定义系统中的核心数据结构，包括Pool、PoolChain和Position类
 */
import { PublicKey } from '@solana/web3.js';
/**
 * BIN数据接口
 */
export interface BinData {
    binId: number;
    x: bigint;
    y: bigint;
    price?: string | number;
}
/**
 * 价格区间接口
 */
export interface PriceRange {
    minPrice: number;
    maxPrice: number;
}
/**
 * 池子类
 * 表示单个DLMM流动性池
 */
export declare class Pool {
    address: PublicKey;
    dlmmPool: any;
    priceRange: PriceRange;
    positions: Position[];
    tokenXDecimals: number;
    tokenYDecimals: number;
    tokenXSymbol: string;
    tokenYSymbol: string;
    /**
     * 构造函数
     */
    constructor(address: PublicKey, dlmmPool: any);
    /**
     * 获取所有bin的ID
     */
    getAllBinIds(): number[];
    /**
     * 获取所有bin数据
     */
    private getAllBins;
    /**
     * 获取格式化的bin范围字符串
     */
    getBinRangeString(): string;
    /**
     * 获取格式化的价格区间字符串
     * 直接从bin数据中获取价格
     */
    getPriceRangeString(): string;
    /**
     * 获取完整的价格范围信息（包含bin和价格）
     */
    getFullPriceRangeString(): string;
    /**
     * 判断当前价格是否在此池范围内
     */
    isPriceInRange(price: number): boolean;
    /**
     * 判断bin是否在此池范围内
     */
    isBinInRange(binId: number): boolean;
    /**
     * 判断该池是否只包含X代币
     */
    hasOnlyTokenX(): boolean;
    /**
     * 判断该池是否只包含Y代币
     */
    hasOnlyTokenY(): boolean;
    /**
     * 判断池是否符合BidAsk模型
     * 修改判断逻辑：
     * - 删除"只含单种代币"的严格条件
     * - 低于当前价格/bin的池子只检查Y代币分布
     * - 高于当前价格/bin的池子只检查X代币分布
     */
    isBidAskCompliant(isHigherThanCurrentPrice: boolean): boolean;
    /**
     * 获取池子总流动性
     */
    getTotalLiquidity(): {
        totalX: bigint;
        totalY: bigint;
    };
    /**
     * 获取格式化的代币数量字符串
     */
    getFormattedLiquidity(): {
        formattedX: string;
        formattedY: string;
    };
    /**
     * 获取池子和头寸的汇总信息
     */
    getSummaryInfo(): any;
    /**
     * 查找池子的所有头寸
     */
    fetchPositions(owner: PublicKey): Promise<void>;
    /**
     * 根据头寸更新价格范围
     * 简化版本，只使用最简单的方法
     */
    private updatePriceRangeFromPositions;
    /**
     * 获取低于当前bin的相邻头寸
     * @param currentBinId 当前活跃bin ID
     */
    getLowerPosition(currentBinId: number): Position | undefined;
    /**
     * 获取高于当前bin的相邻头寸
     * @param currentBinId 当前活跃bin ID
     */
    getHigherPosition(currentBinId: number): Position | undefined;
    /**
     * 获取包含当前bin的头寸
     * @param currentBinId 当前活跃bin ID
     */
    getCurrentPosition(currentBinId: number): Position | undefined;
}
/**
 * 头寸类
 * 表示单个流动性头寸
 */
export declare class Position {
    publicKey: PublicKey;
    data: any;
    binData: BinData[];
    private tokenXDecimals;
    private tokenYDecimals;
    /**
     * 构造函数
     */
    constructor(publicKey: PublicKey, data: any, tokenXDecimals: number, tokenYDecimals: number);
    /**
     * 从头寸数据中提取bin数据
     * 简化版本，优先检查最常见的几种数据结构
     */
    private extractBinData;
    /**
     * 递归查找对象中符合bin数据结构的数组
     * 简化版本，只检查关键字段
     */
    private findBinArrayInObject;
    /**
     * 映射bin数据到标准格式
     */
    private mapBinData;
    /**
     * 获取头寸总流动性
     */
    getTotalLiquidity(): {
        totalX: bigint;
        totalY: bigint;
    };
    /**
     * 获取格式化的代币数量字符串
     */
    getFormattedLiquidity(): {
        formattedX: string;
        formattedY: string;
    };
    /**
     * 获取头寸的价格范围字符串
     */
    getPriceRangeString(): string;
    /**
     * 获取头寸的bin范围
     */
    getBinRange(): {
        minBinId: number;
        maxBinId: number;
    };
    /**
     * 判断bin是否在该头寸范围内
     */
    isBinInRange(binId: number): boolean;
    /**
     * 判断头寸是否符合BidAsk模型
     * @param isHigherThanCurrentBin 头寸是否高于当前bin
     */
    isBidAskCompliant(isHigherThanCurrentBin: boolean): boolean;
}
/**
 * 串联池链类
 * 管理串联的池子集合
 */
export declare class PoolChain {
    private pools;
    private currentPrice;
    private currentBinId;
    /**
     * 添加池子
     */
    addPool(pool: Pool): void;
    /**
     * 按价格范围排序池子
     */
    private sortPools;
    /**
     * 获取所有池子
     */
    getAllPools(): Pool[];
    /**
     * 根据地址获取池子
     */
    getPoolByAddress(address: string): Pool | undefined;
    /**
     * 更新当前价格
     */
    updateCurrentPrice(price: number): void;
    /**
     * 更新当前活跃bin ID
     */
    updateCurrentBinId(binId: number): void;
    /**
     * 获取当前活跃bin所在的池子
     */
    getCurrentPool(): Pool | undefined;
    /**
     * 获取低于当前活跃bin的相邻池子
     */
    getLowerPool(): Pool | undefined;
    /**
     * 获取高于当前活跃bin的相邻池子
     */
    getHigherPool(): Pool | undefined;
    /**
     * 检查相邻头寸是否符合BidAsk模型
     */
    checkNeighboringPoolsCompliance(): {
        lowerPosition?: {
            position: Position;
            isCompliant: boolean;
        };
        currentPosition?: {
            position: Position;
            isCompliant: boolean;
        };
        higherPosition?: {
            position: Position;
            isCompliant: boolean;
        };
    };
}
