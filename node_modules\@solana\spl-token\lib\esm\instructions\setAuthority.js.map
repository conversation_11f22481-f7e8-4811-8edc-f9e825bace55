{"version": 3, "file": "setAuthority.js", "sourceRoot": "", "sources": ["../../../src/instructions/setAuthority.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,uBAAuB,CAAC;AAGnD,OAAO,EAAE,sBAAsB,EAAE,MAAM,iBAAiB,CAAC;AACzD,OAAO,EAAE,gBAAgB,EAAE,MAAM,iBAAiB,CAAC;AACnD,OAAO,EACH,gCAAgC,EAChC,gCAAgC,EAChC,mCAAmC,EACnC,gCAAgC,GACnC,MAAM,cAAc,CAAC;AACtB,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,gBAAgB,EAAE,MAAM,YAAY,CAAC;AAC9C,OAAO,EAAE,sBAAsB,EAAE,MAAM,qBAAqB,CAAC;AAE7D,6CAA6C;AAC7C,MAAM,CAAN,IAAY,aAkBX;AAlBD,WAAY,aAAa;IACrB,6DAAc,CAAA;IACd,mEAAiB,CAAA;IACjB,iEAAgB,CAAA;IAChB,iEAAgB,CAAA;IAChB,2EAAqB,CAAA;IACrB,yEAAoB,CAAA;IACpB,2DAAa,CAAA;IACb,iEAAgB,CAAA;IAChB,2EAAqB,CAAA;IACrB,yFAA4B,CAAA;IAC5B,oFAA0B,CAAA;IAC1B,oGAAkC,CAAA;IAClC,wEAAoB,CAAA;IACpB,kEAAiB,CAAA;IACjB,8EAAuB,CAAA;IACvB,kFAAyB,CAAA;IACzB,sEAAmB,CAAA;AACvB,CAAC,EAlBW,aAAa,KAAb,aAAa,QAkBxB;AASD,iBAAiB;AACjB,MAAM,CAAC,MAAM,2BAA2B,GAAG,MAAM,CAA8B;IAC3E,EAAE,CAAC,aAAa,CAAC;IACjB,EAAE,CAAC,eAAe,CAAC;IACnB,IAAI,sBAAsB,CAAC,cAAc,CAAC;CAC7C,CAAC,CAAC;AAEH;;;;;;;;;;;GAWG;AACH,MAAM,UAAU,6BAA6B,CACzC,OAAkB,EAClB,gBAA2B,EAC3B,aAA4B,EAC5B,YAA8B,EAC9B,eAAuC,EAAE,EACzC,SAAS,GAAG,gBAAgB;IAE5B,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,gBAAgB,EAAE,YAAY,CAAC,CAAC;IAElH,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa;IAC5C,2BAA2B,CAAC,MAAM,CAC9B;QACI,WAAW,EAAE,gBAAgB,CAAC,YAAY;QAC1C,aAAa;QACb,YAAY;KACf,EACD,IAAI,CACP,CAAC;IAEF,OAAO,IAAI,sBAAsB,CAAC;QAC9B,IAAI;QACJ,SAAS;QACT,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,2BAA2B,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;KACpE,CAAC,CAAC;AACP,CAAC;AAiBD;;;;;;;GAOG;AACH,MAAM,UAAU,6BAA6B,CACzC,WAAmC,EACnC,SAAS,GAAG,gBAAgB;IAE5B,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC;QAAE,MAAM,IAAI,mCAAmC,EAAE,CAAC;IAC9F,IAAI,WAAW,CAAC,IAAI,CAAC,MAAM,KAAK,2BAA2B,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC;QACjF,MAAM,IAAI,gCAAgC,EAAE,CAAC;IAEjD,MAAM,EACF,IAAI,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,YAAY,EAAE,EACjD,IAAI,GACP,GAAG,sCAAsC,CAAC,WAAW,CAAC,CAAC;IACxD,IAAI,IAAI,CAAC,WAAW,KAAK,gBAAgB,CAAC,YAAY;QAAE,MAAM,IAAI,gCAAgC,EAAE,CAAC;IACrG,IAAI,CAAC,OAAO,IAAI,CAAC,gBAAgB;QAAE,MAAM,IAAI,gCAAgC,EAAE,CAAC;IAEhF,oBAAoB;IAEpB,OAAO;QACH,SAAS;QACT,IAAI,EAAE;YACF,OAAO;YACP,gBAAgB;YAChB,YAAY;SACf;QACD,IAAI;KACP,CAAC;AACN,CAAC;AAiBD;;;;;;GAMG;AACH,MAAM,UAAU,sCAAsC,CAAC,EACnD,SAAS,EACT,IAAI,EAAE,CAAC,OAAO,EAAE,gBAAgB,EAAE,GAAG,YAAY,CAAC,EAClD,IAAI,GACiB;IACrB,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,2BAA2B,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAE9F,OAAO;QACH,SAAS;QACT,IAAI,EAAE;YACF,OAAO;YACP,gBAAgB;YAChB,YAAY;SACf;QACD,IAAI,EAAE;YACF,WAAW;YACX,aAAa;YACb,YAAY;SACf;KACJ,CAAC;AACN,CAAC"}