/**
 * DLMM串联池流动性管理脚本 - 显示模块
 *
 * 负责所有终端界面显示功能，提供实时状态更新和可视化
 */
/**
 * 池状态枚举
 */
export declare enum PoolStatus {
    NORMAL = "normal",// 正常
    CURRENT = "current",// 当前价格所在池
    ADJUSTING = "adjusting",// 调整中
    WARNING = "warning",// 警告状态
    ERROR = "error"
}
/**
 * 池子显示数据结构
 */
export interface PoolDisplayData {
    address: string;
    binRange: string;
    priceRange: string;
    tokenX: string;
    tokenY: string;
    status: PoolStatus;
    isBidAsk?: boolean;
    positionId?: string;
}
/**
 * 显示管理器类
 */
export declare class Display {
    private isFirstRender;
    private poolsData;
    private currentPrice;
    private lastUpdateTime;
    private statusMessage;
    private currentBinId;
    /**
     * 清除屏幕
     */
    private clearScreen;
    /**
     * 生成表格水平分隔符
     */
    private generateHorizontalLine;
    /**
     * 生成固定宽度的单元格内容（左对齐数据）
     */
    private formatCell;
    /**
     * 生成固定宽度的标题单元格内容（居中对齐）
     */
    private formatHeaderCell;
    /**
     * 渲染表头
     */
    private renderTableHeader;
    /**
     * 渲染表格行
     */
    private renderTableRow;
    /**
     * 更新池子数据
     */
    updatePoolsData(poolsData: PoolDisplayData[]): void;
    /**
     * 更新当前价格
     */
    updateCurrentPrice(price: string): void;
    /**
     * 更新最后更新时间
     */
    updateLastUpdated(time: string): void;
    /**
     * 更新状态消息
     */
    updateStatusMessage(message: string): void;
    /**
     * 显示消息（别名方法，与updateStatusMessage功能相同）
     */
    displayMessage(message: string): void;
    /**
     * 更新当前bin ID
     */
    updateCurrentBinId(binId: string): void;
    /**
     * 渲染概览信息
     */
    private renderSummary;
    /**
     * 渲染进度条
     */
    renderProgressBar(current: number, total: number, width?: number): void;
    /**
     * 渲染完整界面
     */
    render(): void;
}
export declare const display: Display;
