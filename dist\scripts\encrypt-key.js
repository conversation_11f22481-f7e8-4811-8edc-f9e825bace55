"use strict";
/**
 * 私钥加密脚本
 *
 * 用途：加密私钥并存储到文件中，提高安全性
 * 使用方法：
 *   1. 运行 `npm run encrypt-key`
 *   2. 按提示输入私钥和加密密码
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const crypto_1 = require("../utils/crypto");
const config_1 = require("../dlmm-chain-pools-manager/src/config");
const path_1 = __importDefault(require("path"));
/**
 * 主函数
 */
async function main() {
    try {
        console.log('========================================');
        console.log('私钥加密工具');
        console.log('========================================');
        console.log('此工具将加密您的私钥并保存到安全文件中');
        console.log('只有使用正确的密码才能访问加密的私钥');
        console.log('请确保记住您的密码，否则将无法恢复私钥');
        console.log('========================================\n');
        // 显示当前工作目录
        console.log(`当前工作目录: ${process.cwd()}`);
        // 获取要加密的私钥
        let privateKey;
        if (config_1.WALLET_CONFIG.PRIVATE_KEY) {
            console.log('检测到配置文件中存在私钥');
            const useConfigKey = await getYesNoInput('是否使用配置文件中的私钥? (y/n): ');
            if (useConfigKey) {
                privateKey = config_1.WALLET_CONFIG.PRIVATE_KEY;
                console.log('将使用配置文件中的私钥进行加密');
            }
            else {
                privateKey = await (0, crypto_1.getPasswordFromUser)('请输入要加密的私钥: ');
            }
        }
        else {
            privateKey = await (0, crypto_1.getPasswordFromUser)('请输入要加密的私钥: ');
        }
        // 获取加密密码
        const password = await (0, crypto_1.getPasswordFromUser)('请设置加密密码: ');
        const confirmPassword = await (0, crypto_1.getPasswordFromUser)('请再次输入密码确认: ');
        if (password !== confirmPassword) {
            console.error('两次输入的密码不一致，加密已取消');
            process.exit(1);
        }
        // 加密私钥
        console.log('正在加密私钥...');
        const encryptedData = (0, crypto_1.encryptPrivateKey)(privateKey, password);
        // 保存加密的私钥到文件
        const filePath = (0, crypto_1.getEncryptedKeyPath)();
        (0, crypto_1.saveEncryptedKey)(encryptedData, filePath);
        console.log('\n加密完成!');
        console.log(`私钥已安全加密并保存到文件: ${filePath}`);
        console.log('现在您可以从配置文件中删除明文私钥，使用加密的私钥文件代替');
        console.log('程序将在启动时提示您输入密码来解密私钥');
        // 创建一个备份文件到用户主目录
        const os = await Promise.resolve().then(() => __importStar(require('os')));
        const homeDir = os.homedir();
        const backupPath = path_1.default.join(homeDir, crypto_1.ENCRYPTED_KEY_FILENAME);
        (0, crypto_1.saveEncryptedKey)(encryptedData, backupPath);
        console.log(`\n已在您的主目录中创建备份文件: ${backupPath}`);
    }
    catch (error) {
        console.error('加密过程中出错:', error instanceof Error ? error.message : String(error));
        process.exit(1);
    }
}
/**
 * 获取用户的是/否输入
 */
async function getYesNoInput(prompt) {
    while (true) {
        const input = await (0, crypto_1.getPasswordFromUser)(prompt);
        const lowerInput = input.toLowerCase();
        if (lowerInput === 'y' || lowerInput === 'yes') {
            return true;
        }
        else if (lowerInput === 'n' || lowerInput === 'no') {
            return false;
        }
        console.log('请输入 y (yes) 或 n (no)');
    }
}
// 执行主函数
if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch(error => {
        console.error('发生错误:', error instanceof Error ? error.message : String(error));
        process.exit(1);
    });
}
//# sourceMappingURL=encrypt-key.js.map