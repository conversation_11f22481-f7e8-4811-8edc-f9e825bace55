{"name": "dlmm-chain-pools-manager", "version": "1.0.0", "description": "DLMM串联池流动性管理脚本", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "ts-node src/index.ts", "dev": "ts-node-dev --respawn src/index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["solana", "dlmm", "defi", "liquidity"], "author": "", "license": "ISC", "dependencies": {"@meteora-ag/dlmm": "^1.4.3", "@solana/web3.js": "^1.87.6", "bs58": "^5.0.0"}, "devDependencies": {"@types/node": "^20.10.5", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}}