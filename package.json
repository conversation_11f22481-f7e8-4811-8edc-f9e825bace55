{"name": "dlmm-project", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node dist/dlmm-chain-pools-manager/src/index.js", "client": "npx ts-node src/dlmm-client.ts", "example": "npx ts-node src/dlmm-example.ts", "liquidity": "npx ts-node src/manage-liquidity.ts", "debug": "DEBUG=true npx ts-node --transpile-only src/dlmm-chain-pools-manager/src/index.ts", "build": "tsc", "encrypt-key": "node dist/scripts/encrypt-key.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@meteora-ag/dlmm": "^1.5.0", "@solana/web3.js": "^1.91.1", "axios": "^1.9.0"}, "devDependencies": {"@types/bn.js": "^5.1.5", "@types/bs58": "^4.0.4", "@types/node": "^20.12.9", "ts-node": "^10.9.2", "typescript": "^5.4.2"}}