{"version": 3, "file": "setAuthority.js", "sourceRoot": "", "sources": ["../../../src/instructions/setAuthority.ts"], "names": [], "mappings": ";;;AA8DA,sEAyBC;AAyBD,sEA0BC;AAwBD,wFAoBC;AAtLD,yDAAmD;AAGnD,6CAAyD;AACzD,kDAAmD;AACnD,4CAKsB;AACtB,+CAA2C;AAC3C,yCAA8C;AAC9C,0DAA6D;AAE7D,6CAA6C;AAC7C,IAAY,aAkBX;AAlBD,WAAY,aAAa;IACrB,6DAAc,CAAA;IACd,mEAAiB,CAAA;IACjB,iEAAgB,CAAA;IAChB,iEAAgB,CAAA;IAChB,2EAAqB,CAAA;IACrB,yEAAoB,CAAA;IACpB,2DAAa,CAAA;IACb,iEAAgB,CAAA;IAChB,2EAAqB,CAAA;IACrB,yFAA4B,CAAA;IAC5B,oFAA0B,CAAA;IAC1B,oGAAkC,CAAA;IAClC,wEAAoB,CAAA;IACpB,kEAAiB,CAAA;IACjB,8EAAuB,CAAA;IACvB,kFAAyB,CAAA;IACzB,sEAAmB,CAAA;AACvB,CAAC,EAlBW,aAAa,6BAAb,aAAa,QAkBxB;AASD,iBAAiB;AACJ,QAAA,2BAA2B,GAAG,IAAA,sBAAM,EAA8B;IAC3E,IAAA,kBAAE,EAAC,aAAa,CAAC;IACjB,IAAA,kBAAE,EAAC,eAAe,CAAC;IACnB,IAAI,yCAAsB,CAAC,cAAc,CAAC;CAC7C,CAAC,CAAC;AAEH;;;;;;;;;;;GAWG;AACH,SAAgB,6BAA6B,CACzC,OAAkB,EAClB,gBAA2B,EAC3B,aAA4B,EAC5B,YAA8B,EAC9B,eAAuC,EAAE,EACzC,SAAS,GAAG,+BAAgB;IAE5B,MAAM,IAAI,GAAG,IAAA,wBAAU,EAAC,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE,gBAAgB,EAAE,YAAY,CAAC,CAAC;IAElH,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa;IAC5C,mCAA2B,CAAC,MAAM,CAC9B;QACI,WAAW,EAAE,2BAAgB,CAAC,YAAY;QAC1C,aAAa;QACb,YAAY;KACf,EACD,IAAI,CACP,CAAC;IAEF,OAAO,IAAI,gCAAsB,CAAC;QAC9B,IAAI;QACJ,SAAS;QACT,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,mCAA2B,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;KACpE,CAAC,CAAC;AACP,CAAC;AAiBD;;;;;;;GAOG;AACH,SAAgB,6BAA6B,CACzC,WAAmC,EACnC,SAAS,GAAG,+BAAgB;IAE5B,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC;QAAE,MAAM,IAAI,+CAAmC,EAAE,CAAC;IAC9F,IAAI,WAAW,CAAC,IAAI,CAAC,MAAM,KAAK,mCAA2B,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC;QACjF,MAAM,IAAI,4CAAgC,EAAE,CAAC;IAEjD,MAAM,EACF,IAAI,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,YAAY,EAAE,EACjD,IAAI,GACP,GAAG,sCAAsC,CAAC,WAAW,CAAC,CAAC;IACxD,IAAI,IAAI,CAAC,WAAW,KAAK,2BAAgB,CAAC,YAAY;QAAE,MAAM,IAAI,4CAAgC,EAAE,CAAC;IACrG,IAAI,CAAC,OAAO,IAAI,CAAC,gBAAgB;QAAE,MAAM,IAAI,4CAAgC,EAAE,CAAC;IAEhF,oBAAoB;IAEpB,OAAO;QACH,SAAS;QACT,IAAI,EAAE;YACF,OAAO;YACP,gBAAgB;YAChB,YAAY;SACf;QACD,IAAI;KACP,CAAC;AACN,CAAC;AAiBD;;;;;;GAMG;AACH,SAAgB,sCAAsC,CAAC,EACnD,SAAS,EACT,IAAI,EAAE,CAAC,OAAO,EAAE,gBAAgB,EAAE,GAAG,YAAY,CAAC,EAClD,IAAI,GACiB;IACrB,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,mCAA2B,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAE9F,OAAO;QACH,SAAS;QACT,IAAI,EAAE;YACF,OAAO;YACP,gBAAgB;YAChB,YAAY;SACf;QACD,IAAI,EAAE;YACF,WAAW;YACX,aAAa;YACb,YAAY;SACf;KACJ,CAAC;AACN,CAAC"}