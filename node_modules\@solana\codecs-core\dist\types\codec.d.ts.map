{"version": 3, "file": "codec.d.ts", "sourceRoot": "", "sources": ["../../src/codec.ts"], "names": [], "mappings": "AAMA,OAAO,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAE3D;;GAEG;AACH,MAAM,MAAM,MAAM,GAAG,MAAM,CAAC;AAE5B,KAAK,WAAW,CAAC,KAAK,IAAI;IACtB,uEAAuE;IACvE,QAAQ,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,kBAAkB,CAAC;IACtD;;;OAGG;IACH,QAAQ,CAAC,KAAK,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,KAAK,MAAM,CAAC;CAC/E,CAAC;AAEF,MAAM,MAAM,gBAAgB,CAAC,KAAK,EAAE,KAAK,SAAS,MAAM,GAAG,MAAM,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG;IACtF,oDAAoD;IACpD,QAAQ,CAAC,SAAS,EAAE,KAAK,CAAC;CAC7B,CAAC;AAEF,MAAM,MAAM,mBAAmB,CAAC,KAAK,IAAI,WAAW,CAAC,KAAK,CAAC,GAAG;IAC1D,oDAAoD;IACpD,QAAQ,CAAC,gBAAgB,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,MAAM,CAAC;IACpD,wEAAwE;IACxE,QAAQ,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC;CAC7B,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,OAAO,CAAC,KAAK,IAAI,gBAAgB,CAAC,KAAK,CAAC,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC;AAElF,KAAK,WAAW,CAAC,GAAG,IAAI;IACpB,oGAAoG;IACpG,QAAQ,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,kBAAkB,GAAG,UAAU,EAAE,MAAM,CAAC,EAAE,MAAM,KAAK,GAAG,CAAC;IAClF;;;OAGG;IACH,QAAQ,CAAC,IAAI,EAAE,CAAC,KAAK,EAAE,kBAAkB,GAAG,UAAU,EAAE,MAAM,EAAE,MAAM,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;CAC5F,CAAC;AAEF,MAAM,MAAM,gBAAgB,CAAC,GAAG,EAAE,KAAK,SAAS,MAAM,GAAG,MAAM,IAAI,WAAW,CAAC,GAAG,CAAC,GAAG;IAClF,oDAAoD;IACpD,QAAQ,CAAC,SAAS,EAAE,KAAK,CAAC;CAC7B,CAAC;AAEF,MAAM,MAAM,mBAAmB,CAAC,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,GAAG;IACtD,wEAAwE;IACxE,QAAQ,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC;CAC7B,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,OAAO,CAAC,GAAG,IAAI,gBAAgB,CAAC,GAAG,CAAC,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC;AAE5E,MAAM,MAAM,cAAc,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,GAAG,KAAK,EAAE,KAAK,SAAS,MAAM,GAAG,MAAM,IAAI,gBAAgB,CAC1G,GAAG,EACH,KAAK,CACR,GACG,gBAAgB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAEnC,MAAM,MAAM,iBAAiB,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,GAAG,KAAK,IAAI,mBAAmB,CAAC,GAAG,CAAC,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC;AAExH;;;;;;;;GAQG;AACH,MAAM,MAAM,KAAK,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,GAAG,KAAK,IAAI,cAAc,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,iBAAiB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAEjH;;GAEG;AACH,wBAAgB,cAAc,CAAC,KAAK,EAChC,KAAK,EAAE,KAAK,EACZ,OAAO,EAAE;IAAE,SAAS,EAAE,MAAM,CAAA;CAAE,GAAG;IAAE,gBAAgB,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK,MAAM,CAAA;CAAE,GAChF,MAAM,CAER;AAED,+EAA+E;AAC/E,wBAAgB,aAAa,CAAC,KAAK,EAAE,KAAK,SAAS,MAAM,EACrD,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,QAAQ,CAAC,GACxD,gBAAgB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAClC,wBAAgB,aAAa,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE,QAAQ,CAAC,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC;AACtH,wBAAgB,aAAa,CAAC,KAAK,EAC/B,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE,QAAQ,CAAC,GAC9F,OAAO,CAAC,KAAK,CAAC,CAAC;AAclB,8EAA8E;AAC9E,wBAAgB,aAAa,CAAC,GAAG,EAAE,KAAK,SAAS,MAAM,EACnD,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,QAAQ,CAAC,GACtD,gBAAgB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAChC,wBAAgB,aAAa,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC;AAChH,wBAAgB,aAAa,CAAC,GAAG,EAC7B,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,GAC1F,OAAO,CAAC,GAAG,CAAC,CAAC;AAUhB,wGAAwG;AACxG,wBAAgB,WAAW,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,GAAG,KAAK,EAAE,KAAK,SAAS,MAAM,GAAG,MAAM,EACvF,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GACpE,cAAc,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;AACrC,wBAAgB,WAAW,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,GAAG,KAAK,EACxD,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAChE,iBAAiB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACjC,wBAAgB,WAAW,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,GAAG,KAAK,EACxD,KAAK,EACC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GACrD,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAC/D,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAiBrB,wBAAgB,WAAW,CAAC,KAAK,EAAE,KAAK,SAAS,MAAM,EACnD,OAAO,EAAE,gBAAgB,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,mBAAmB,CAAC,KAAK,CAAC,GACrE,OAAO,IAAI,gBAAgB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC7C,wBAAgB,WAAW,CAAC,GAAG,EAAE,KAAK,SAAS,MAAM,EACjD,OAAO,EAAE,gBAAgB,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,mBAAmB,CAAC,GAAG,CAAC,GACjE,OAAO,IAAI,gBAAgB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AAC3C,wBAAgB,WAAW,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,EAAE,KAAK,SAAS,MAAM,EACtE,KAAK,EAAE,cAAc,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,iBAAiB,CAAC,KAAK,EAAE,GAAG,CAAC,GACzE,KAAK,IAAI,cAAc,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;AAC9C,wBAAgB,WAAW,CAAC,KAAK,SAAS,MAAM,EAC5C,KAAK,EAAE;IAAE,SAAS,EAAE,KAAK,CAAA;CAAE,GAAG;IAAE,OAAO,CAAC,EAAE,MAAM,CAAA;CAAE,GACnD,KAAK,IAAI;IAAE,SAAS,EAAE,KAAK,CAAA;CAAE,CAAC;AAKjC,wBAAgB,iBAAiB,CAAC,KAAK,EAAE,KAAK,SAAS,MAAM,EACzD,OAAO,EAAE,gBAAgB,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,mBAAmB,CAAC,KAAK,CAAC,GACrE,OAAO,CAAC,OAAO,IAAI,gBAAgB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACrD,wBAAgB,iBAAiB,CAAC,GAAG,EAAE,KAAK,SAAS,MAAM,EACvD,OAAO,EAAE,gBAAgB,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,mBAAmB,CAAC,GAAG,CAAC,GACjE,OAAO,CAAC,OAAO,IAAI,gBAAgB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;AACnD,wBAAgB,iBAAiB,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,EAAE,KAAK,SAAS,MAAM,EAC5E,KAAK,EAAE,cAAc,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,iBAAiB,CAAC,KAAK,EAAE,GAAG,CAAC,GACzE,OAAO,CAAC,KAAK,IAAI,cAAc,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;AACtD,wBAAgB,iBAAiB,CAAC,KAAK,SAAS,MAAM,EAClD,KAAK,EAAE;IAAE,SAAS,EAAE,KAAK,CAAA;CAAE,GAAG;IAAE,OAAO,CAAC,EAAE,MAAM,CAAA;CAAE,GACnD,OAAO,CAAC,KAAK,IAAI;IAAE,SAAS,EAAE,KAAK,CAAA;CAAE,CAAC;AASzC,wBAAgB,cAAc,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,IAAI,mBAAmB,CAAC,KAAK,CAAC,CAAC;AACtG,wBAAgB,cAAc,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,OAAO,IAAI,mBAAmB,CAAC,GAAG,CAAC,CAAC;AAChG,wBAAgB,cAAc,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,EACnD,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,GACzB,KAAK,IAAI,iBAAiB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC1C,wBAAgB,cAAc,CAAC,KAAK,EAAE;IAAE,SAAS,EAAE,MAAM,CAAA;CAAE,GAAG;IAAE,OAAO,CAAC,EAAE,MAAM,CAAA;CAAE,GAAG,KAAK,IAAI;IAAE,OAAO,CAAC,EAAE,MAAM,CAAA;CAAE,CAAC;AAKnH,wBAAgB,oBAAoB,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,OAAO,IAAI,mBAAmB,CAAC,CAAC,CAAC,CAAC;AACxG,wBAAgB,oBAAoB,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,OAAO,IAAI,mBAAmB,CAAC,CAAC,CAAC,CAAC;AACxG,wBAAgB,oBAAoB,CAAC,KAAK,EAAE,GAAG,SAAS,KAAK,EACzD,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,GACzB,OAAO,CAAC,KAAK,IAAI,iBAAiB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAClD,wBAAgB,oBAAoB,CAChC,KAAK,EAAE;IAAE,SAAS,EAAE,MAAM,CAAA;CAAE,GAAG;IAAE,OAAO,CAAC,EAAE,MAAM,CAAA;CAAE,GACpD,OAAO,CAAC,KAAK,IAAI;IAAE,OAAO,CAAC,EAAE,MAAM,CAAA;CAAE,CAAC"}