{"version": 3, "sources": ["../src/option.ts", "../src/unwrap-option.ts", "../src/option-codec.ts", "../src/unwrap-option-recursively.ts"], "names": ["transformEncoder", "getUnitEncoder", "getBooleanEncoder", "getU8Encoder", "assertIsFixedSize", "fixEncoderSize", "getConstantEncoder", "getUnionEncoder", "getTupleEncoder", "transformDecoder", "getUnitDecoder", "getBooleanDecoder", "getU8Decoder", "fixDecoderSize", "getConstantDecoder", "getUnionDecoder", "getTupleDecoder", "containsBytes", "combineCodec"], "mappings": ";;;;;;;AAkCO,IAAM,OAAO,CAAI,KAAA,MAAyB,EAAE,QAAA,EAAU,QAAQ,KAAM,EAAA,EAAA;AAOpE,IAAM,IAAO,GAAA,OAAqB,EAAE,QAAA,EAAU,MAAO,EAAA,EAAA;AAKrD,IAAM,WAAW,CAAc,KAAA,KAClC,CAAC,EACG,SACA,OAAO,KAAA,KAAU,QACjB,IAAA,UAAA,IAAc,UACZ,KAAM,CAAA,QAAA,KAAa,UAAU,OAAW,IAAA,KAAA,IAAU,MAAM,QAAa,KAAA,MAAA,CAAA,EAAA;AAMxE,IAAM,MAAS,GAAA,CAAI,MAAyC,KAAA,MAAA,CAAO,QAAa,KAAA,OAAA;AAKhF,IAAM,MAAS,GAAA,CAAI,MAAsC,KAAA,MAAA,CAAO,QAAa,KAAA,OAAA;;;ACtD7E,SAAS,YAAA,CAA0B,QAAmB,QAA2B,EAAA;AACpF,EAAA,IAAI,MAAO,CAAA,MAAM,CAAG,EAAA,OAAO,MAAO,CAAA,KAAA,CAAA;AAClC,EAAO,OAAA,QAAA,GAAW,UAAc,GAAA,IAAA,CAAA;AACpC,CAAA;AAKa,IAAA,YAAA,GAAe,CAAI,QAAmC,KAAA,QAAA,KAAa,OAAO,IAAK,CAAA,QAAQ,IAAI,IAAQ,GAAA;;;ACwFzG,SAAS,gBACZ,CAAA,IAAA,EACA,MAA2C,GAAA,EACX,EAAA;AAChC,EAAA,MAAM,UAAU,MAAM;AAClB,IAAI,IAAA,MAAA,CAAO,WAAW,IAAM,EAAA;AACxB,MAAA,OAAOA,2BAAiB,CAAAC,mCAAA,EAAkB,EAAA,CAAC,aAAsB,KAAS,CAAA,CAAA,CAAA;AAAA,KAC9E;AACA,IAAA,OAAOC,uCAAkB,EAAE,IAAA,EAAM,OAAO,MAAU,IAAAC,0BAAA,IAAgB,CAAA,CAAA;AAAA,GACnE,GAAA,CAAA;AACH,EAAA,MAAM,aAAa,MAAM;AACrB,IAAI,IAAA,MAAA,CAAO,cAAc,QAAU,EAAA;AAC/B,MAAAC,4BAAA,CAAkB,IAAI,CAAA,CAAA;AACtB,MAAA,OAAOC,yBAAe,CAAAJ,mCAAA,EAAkB,EAAA,IAAA,CAAK,SAAS,CAAA,CAAA;AAAA,KAC1D;AACA,IAAI,IAAA,CAAC,OAAO,SAAW,EAAA;AACnB,MAAA,OAAOA,mCAAe,EAAA,CAAA;AAAA,KAC1B;AACA,IAAO,OAAAK,uCAAA,CAAmB,OAAO,SAAS,CAAA,CAAA;AAAA,GAC3C,GAAA,CAAA;AAEH,EAAO,OAAAC,oCAAA;AAAA,IACH;AAAA,MACIP,2BAAA,CAAiBQ,qCAAgB,CAAC,MAAA,EAAQ,SAAS,CAAC,CAAA,EAAG,CAAC,MAAyC,KAAA;AAAA,QAC7F,KAAA;AAAA,QACA,KAAA,CAAA;AAAA,OACH,CAAA;AAAA,MACDR,2BAAA,CAAiBQ,qCAAgB,CAAC,MAAA,EAAQ,IAAI,CAAC,CAAA,EAAG,CAAC,KAAiD,KAAA;AAAA,QAChG,IAAA;AAAA,QACA,SAAS,KAAK,CAAA,IAAK,OAAO,KAAK,CAAA,GAAI,MAAM,KAAQ,GAAA,KAAA;AAAA,OACpD,CAAA;AAAA,KACL;AAAA,IACA,CAAW,OAAA,KAAA;AACP,MAAA,MAAM,SAAS,QAAgB,CAAA,OAAO,CAAI,GAAA,OAAA,GAAU,aAAa,OAAO,CAAA,CAAA;AACxE,MAAO,OAAA,MAAA,CAAO,MAAO,CAAA,MAAM,CAAC,CAAA,CAAA;AAAA,KAChC;AAAA,GACJ,CAAA;AACJ,CAAA;AAwBO,SAAS,gBACZ,CAAA,IAAA,EACA,MAA2C,GAAA,EACvB,EAAA;AACpB,EAAA,MAAM,UAAU,MAAM;AAClB,IAAI,IAAA,MAAA,CAAO,WAAW,IAAM,EAAA;AACxB,MAAA,OAAOC,2BAAiB,CAAAC,mCAAA,EAAkB,EAAA,MAAM,KAAK,CAAA,CAAA;AAAA,KACzD;AACA,IAAA,OAAOC,uCAAkB,EAAE,IAAA,EAAM,OAAO,MAAU,IAAAC,0BAAA,IAAgB,CAAA,CAAA;AAAA,GACnE,GAAA,CAAA;AACH,EAAA,MAAM,aAAa,MAAM;AACrB,IAAI,IAAA,MAAA,CAAO,cAAc,QAAU,EAAA;AAC/B,MAAAR,4BAAA,CAAkB,IAAI,CAAA,CAAA;AACtB,MAAA,OAAOS,yBAAe,CAAAH,mCAAA,EAAkB,EAAA,IAAA,CAAK,SAAS,CAAA,CAAA;AAAA,KAC1D;AACA,IAAI,IAAA,CAAC,OAAO,SAAW,EAAA;AACnB,MAAA,OAAOA,mCAAe,EAAA,CAAA;AAAA,KAC1B;AACA,IAAO,OAAAI,uCAAA,CAAmB,OAAO,SAAS,CAAA,CAAA;AAAA,GAC3C,GAAA,CAAA;AAEH,EAAO,OAAAC,oCAAA;AAAA,IACH;AAAA,MACIN,2BAAA,CAAiBO,qCAAgB,CAAC,MAAA,EAAQ,SAAS,CAAC,CAAA,EAAG,MAAM,IAAA,EAAW,CAAA;AAAA,MACxEP,2BAAiB,CAAAO,oCAAA,CAAgB,CAAC,MAAA,EAAQ,IAAI,CAAC,CAAA,EAAG,CAAC,GAAG,KAAK,CAAM,KAAA,IAAA,CAAK,KAAK,CAAC,CAAA;AAAA,KAChF;AAAA,IACA,CAAC,OAAO,MAAW,KAAA;AACf,MAAA,IAAI,MAAO,CAAA,MAAA,KAAW,IAAQ,IAAA,CAAC,OAAO,SAAW,EAAA;AAC7C,QAAO,OAAA,MAAA,CAAO,MAAS,GAAA,KAAA,CAAM,MAAM,CAAA,CAAA;AAAA,OACvC;AACA,MAAA,IAAI,MAAO,CAAA,MAAA,KAAW,IAAQ,IAAA,MAAA,CAAO,aAAa,IAAM,EAAA;AACpD,QAAA,MAAM,SACF,GAAA,MAAA,CAAO,SAAc,KAAA,QAAA,GAAW,IAAI,UAAA,CAAW,SAAU,CAAA,SAAS,CAAE,CAAA,IAAA,CAAK,CAAC,CAAA,GAAI,MAAO,CAAA,SAAA,CAAA;AACzF,QAAA,OAAOC,wBAAc,CAAA,KAAA,EAAO,SAAW,EAAA,MAAM,IAAI,CAAI,GAAA,CAAA,CAAA;AAAA,OACzD;AACA,MAAA,OAAO,OAAO,MAAO,CAAA,IAAA,CAAK,OAAO,MAAM,CAAA,CAAE,CAAC,CAAC,CAAA,CAAA;AAAA,KAC/C;AAAA,GACJ,CAAA;AACJ,CAAA;AAwBO,SAAS,cACZ,CAAA,IAAA,EACA,MAAyC,GAAA,EACE,EAAA;AAE3C,EAAO,OAAAC,uBAAA;AAAA,IACH,gBAAA,CAAwB,MAAM,MAAoB,CAAA;AAAA,IAClD,gBAAA,CAAsB,MAAM,MAAoB,CAAA;AAAA,GACpD,CAAA;AACJ,CAAA;;;ACtLO,SAAS,uBAAA,CAAqC,OAAU,QAA2C,EAAA;AAEtG,EAAA,IAAI,CAAC,KAAA,IAAS,WAAY,CAAA,MAAA,CAAO,KAAK,CAAG,EAAA;AACrC,IAAO,OAAA,KAAA,CAAA;AAAA,GACX;AAEA,EAAM,MAAA,IAAA,GAAO,CAAI,CACZ,KAAA,QAAA,GAAW,wBAAwB,CAAG,EAAA,QAAQ,CAAI,GAAA,uBAAA,CAAwB,CAAC,CAAA,CAAA;AAGhF,EAAI,IAAA,QAAA,CAAS,KAAK,CAAG,EAAA;AACjB,IAAA,IAAI,OAAO,KAAK,CAAA,EAAU,OAAA,IAAA,CAAK,MAAM,KAAK,CAAA,CAAA;AAC1C,IAAQ,OAAA,QAAA,GAAW,UAAa,GAAA,IAAA,CAAA;AAAA,GACpC;AAGA,EAAI,IAAA,KAAA,CAAM,OAAQ,CAAA,KAAK,CAAG,EAAA;AACtB,IAAO,OAAA,KAAA,CAAM,IAAI,IAAI,CAAA,CAAA;AAAA,GACzB;AACA,EAAI,IAAA,OAAO,UAAU,QAAU,EAAA;AAC3B,IAAA,OAAO,OAAO,WAAY,CAAA,MAAA,CAAO,QAAQ,KAAK,CAAA,CAAE,IAAI,CAAC,CAAC,CAAG,EAAA,CAAC,MAAM,CAAC,CAAA,EAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA,CAAA;AAAA,GACjF;AACA,EAAO,OAAA,KAAA,CAAA;AACX", "file": "index.node.cjs", "sourcesContent": ["/**\n * An implementation of the Rust Option type in JavaScript.\n * It can be one of the following:\n * - <code>{@link Some}<T></code>: Meaning there is a value of type T.\n * - <code>{@link None}</code>: Meaning there is no value.\n */\nexport type Option<T> = None | Some<T>;\n\n/**\n * Defines a looser type that can be used when serializing an {@link Option}.\n * This allows us to pass null or the Option value directly whilst still\n * supporting the Option type for use-cases that need more type safety.\n */\nexport type OptionOrNullable<T> = Option<T> | T | null;\n\n/**\n * Represents an option of type `T` that has a value.\n *\n * @see {@link Option}\n */\nexport type Some<T> = Readonly<{ __option: 'Some'; value: T }>;\n\n/**\n * Represents an option of type `T` that has no value.\n *\n * @see {@link Option}\n */\nexport type None = Readonly<{ __option: 'None' }>;\n\n/**\n * Creates a new {@link Option} of type `T` that has a value.\n *\n * @see {@link Option}\n */\nexport const some = <T>(value: T): Option<T> => ({ __option: 'Some', value });\n\n/**\n * Creates a new {@link Option} of type `T` that has no value.\n *\n * @see {@link Option}\n */\nexport const none = <T>(): Option<T> => ({ __option: 'None' });\n\n/**\n * Whether the given data is an {@link Option}.\n */\nexport const isOption = <T = unknown>(input: unknown): input is Option<T> =>\n    !!(\n        input &&\n        typeof input === 'object' &&\n        '__option' in input &&\n        ((input.__option === 'Some' && 'value' in input) || input.__option === 'None')\n    );\n\n/**\n * Whether the given {@link Option} is a {@link Some}.\n */\nexport const isSome = <T>(option: Option<T>): option is Some<T> => option.__option === 'Some';\n\n/**\n * Whether the given {@link Option} is a {@link None}.\n */\nexport const isNone = <T>(option: Option<T>): option is None => option.__option === 'None';\n", "import { isSome, none, Option, some } from './option';\n\n/**\n * Unwraps the value of an {@link Option} of type `T`\n * or returns a fallback value that defaults to `null`.\n */\nexport function unwrapOption<T>(option: Option<T>): T | null;\nexport function unwrapOption<T, U>(option: Option<T>, fallback: () => U): T | U;\nexport function unwrapOption<T, U = null>(option: Option<T>, fallback?: () => U): T | U {\n    if (isSome(option)) return option.value;\n    return fallback ? fallback() : (null as U);\n}\n\n/**\n * Wraps a nullable value into an {@link Option}.\n */\nexport const wrapNullable = <T>(nullable: T | null): Option<T> => (nullable !== null ? some(nullable) : none<T>());\n", "import {\n    assertIsFixedSize,\n    Codec,\n    combineCodec,\n    containsBytes,\n    Decoder,\n    Encoder,\n    fixDecoderSize,\n    FixedSizeCodec,\n    FixedSizeDecoder,\n    FixedSizeEncoder,\n    fixEncoderSize,\n    ReadonlyUint8Array,\n    transformDecoder,\n    transformEncoder,\n    VariableSizeCodec,\n    VariableSizeDecoder,\n    VariableSizeEncoder,\n} from '@solana/codecs-core';\nimport {\n    getBooleanDecoder,\n    getBooleanEncoder,\n    getConstantDecoder,\n    getConstantEncoder,\n    getTupleDecoder,\n    getTupleEncoder,\n    getUnionDecoder,\n    getUnionEncoder,\n    getUnitDecoder,\n    getUnitEncoder,\n} from '@solana/codecs-data-structures';\nimport {\n    FixedSizeNumberCodec,\n    FixedSizeNumberDecoder,\n    FixedSizeNumberEncoder,\n    getU8Decoder,\n    getU8Encoder,\n    NumberCodec,\n    NumberDecoder,\n    NumberEncoder,\n} from '@solana/codecs-numbers';\n\nimport { isOption, isSome, None, none, Option, OptionOrNullable, Some, some } from './option';\nimport { wrapNullable } from './unwrap-option';\n\n/** Defines the config for Option codecs. */\nexport type OptionCodecConfig<TPrefix extends NumberCodec | NumberDecoder | NumberEncoder> = {\n    /**\n     * Defines how the `None` value should be represented.\n     *\n     * By default, no none value is used. This means a `None` value will be\n     * represented by the absence of the item.\n     *\n     * When `'zeroes'` is provided, a `None` value will skip the bytes that would\n     * have been used for the item. Note that this returns a fixed-size codec\n     * and thus will only work if the item codec is of fixed size.\n     *\n     * When a custom byte array is provided, a `None` value will be represented\n     * by the provided byte array. Note that this returns a variable-size codec\n     * since the byte array representing `None` does not need to match the size\n     * of the item codec.\n     *\n     * @defaultValue No none value is used.\n     */\n    noneValue?: ReadonlyUint8Array | 'zeroes';\n\n    /**\n     * The codec to use for the boolean prefix, if any.\n     *\n     * By default a `u8` number is used as a prefix to determine if the value is `None`.\n     * The value `0` is encoded for `None` and `1` if the value is present.\n     * This can be set to any number codec to customize the prefix.\n     *\n     * When `null` is provided, no prefix is used and the `noneValue` is used to\n     * determine if the value is `None`. If no `noneValue` is provided, then the\n     * absence of any bytes is used to determine if the value is `None`.\n     *\n     * @defaultValue `u8` prefix.\n     */\n    prefix?: TPrefix | null;\n};\n\n/**\n * Creates a encoder for an optional value using the `Option<T>` type.\n *\n * @param item - The encoder to use for the value that may be present.\n * @param config - A set of config for the encoder.\n */\nexport function getOptionEncoder<TFrom, TSize extends number>(\n    item: FixedSizeEncoder<TFrom, TSize>,\n    config: OptionCodecConfig<NumberEncoder> & { noneValue: 'zeroes'; prefix: null },\n): FixedSizeEncoder<OptionOrNullable<TFrom>, TSize>;\nexport function getOptionEncoder<TFrom>(\n    item: FixedSizeEncoder<TFrom>,\n    config: OptionCodecConfig<FixedSizeNumberEncoder> & { noneValue: 'zeroes' },\n): FixedSizeEncoder<OptionOrNullable<TFrom>>;\nexport function getOptionEncoder<TFrom>(\n    item: FixedSizeEncoder<TFrom>,\n    config: OptionCodecConfig<NumberEncoder> & { noneValue: 'zeroes' },\n): VariableSizeEncoder<OptionOrNullable<TFrom>>;\nexport function getOptionEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config?: OptionCodecConfig<NumberEncoder> & { noneValue?: ReadonlyUint8Array },\n): VariableSizeEncoder<OptionOrNullable<TFrom>>;\nexport function getOptionEncoder<TFrom>(\n    item: Encoder<TFrom>,\n    config: OptionCodecConfig<NumberEncoder> = {},\n): Encoder<OptionOrNullable<TFrom>> {\n    const prefix = (() => {\n        if (config.prefix === null) {\n            return transformEncoder(getUnitEncoder(), (_boolean: boolean) => undefined);\n        }\n        return getBooleanEncoder({ size: config.prefix ?? getU8Encoder() });\n    })();\n    const noneValue = (() => {\n        if (config.noneValue === 'zeroes') {\n            assertIsFixedSize(item);\n            return fixEncoderSize(getUnitEncoder(), item.fixedSize);\n        }\n        if (!config.noneValue) {\n            return getUnitEncoder();\n        }\n        return getConstantEncoder(config.noneValue);\n    })();\n\n    return getUnionEncoder(\n        [\n            transformEncoder(getTupleEncoder([prefix, noneValue]), (_value: None | null): [boolean, void] => [\n                false,\n                undefined,\n            ]),\n            transformEncoder(getTupleEncoder([prefix, item]), (value: Some<TFrom> | TFrom): [boolean, TFrom] => [\n                true,\n                isOption(value) && isSome(value) ? value.value : value,\n            ]),\n        ],\n        variant => {\n            const option = isOption<TFrom>(variant) ? variant : wrapNullable(variant);\n            return Number(isSome(option));\n        },\n    );\n}\n\n/**\n * Creates a decoder for an optional value using the `Option<T>` type.\n *\n * @param item - The decoder to use for the value that may be present.\n * @param config - A set of config for the decoder.\n */\nexport function getOptionDecoder<TTo, TSize extends number>(\n    item: FixedSizeDecoder<TTo, TSize>,\n    config: OptionCodecConfig<NumberDecoder> & { noneValue: 'zeroes'; prefix: null },\n): FixedSizeDecoder<Option<TTo>, TSize>;\nexport function getOptionDecoder<TTo>(\n    item: FixedSizeDecoder<TTo>,\n    config: OptionCodecConfig<FixedSizeNumberDecoder> & { noneValue: 'zeroes' },\n): FixedSizeDecoder<Option<TTo>>;\nexport function getOptionDecoder<TTo>(\n    item: FixedSizeDecoder<TTo>,\n    config: OptionCodecConfig<NumberDecoder> & { noneValue: 'zeroes' },\n): VariableSizeDecoder<Option<TTo>>;\nexport function getOptionDecoder<TTo>(\n    item: Decoder<TTo>,\n    config?: OptionCodecConfig<NumberDecoder> & { noneValue?: ReadonlyUint8Array },\n): VariableSizeDecoder<Option<TTo>>;\nexport function getOptionDecoder<TTo>(\n    item: Decoder<TTo>,\n    config: OptionCodecConfig<NumberDecoder> = {},\n): Decoder<Option<TTo>> {\n    const prefix = (() => {\n        if (config.prefix === null) {\n            return transformDecoder(getUnitDecoder(), () => false);\n        }\n        return getBooleanDecoder({ size: config.prefix ?? getU8Decoder() });\n    })();\n    const noneValue = (() => {\n        if (config.noneValue === 'zeroes') {\n            assertIsFixedSize(item);\n            return fixDecoderSize(getUnitDecoder(), item.fixedSize);\n        }\n        if (!config.noneValue) {\n            return getUnitDecoder();\n        }\n        return getConstantDecoder(config.noneValue);\n    })();\n\n    return getUnionDecoder(\n        [\n            transformDecoder(getTupleDecoder([prefix, noneValue]), () => none<TTo>()),\n            transformDecoder(getTupleDecoder([prefix, item]), ([, value]) => some(value)),\n        ],\n        (bytes, offset) => {\n            if (config.prefix === null && !config.noneValue) {\n                return Number(offset < bytes.length);\n            }\n            if (config.prefix === null && config.noneValue != null) {\n                const zeroValue =\n                    config.noneValue === 'zeroes' ? new Uint8Array(noneValue.fixedSize).fill(0) : config.noneValue;\n                return containsBytes(bytes, zeroValue, offset) ? 0 : 1;\n            }\n            return Number(prefix.read(bytes, offset)[0]);\n        },\n    );\n}\n\n/**\n * Creates a codec for an optional value using the `Option<T>` type.\n *\n * @param item - The codec to use for the value that may be present.\n * @param config - A set of config for the codec.\n */\nexport function getOptionCodec<TFrom, TTo extends TFrom, TSize extends number>(\n    item: FixedSizeCodec<TFrom, TTo, TSize>,\n    config: OptionCodecConfig<NumberCodec> & { noneValue: 'zeroes'; prefix: null },\n): FixedSizeCodec<OptionOrNullable<TFrom>, Option<TTo>, TSize>;\nexport function getOptionCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: FixedSizeCodec<TFrom, TTo>,\n    config: OptionCodecConfig<FixedSizeNumberCodec> & { noneValue: 'zeroes' },\n): FixedSizeCodec<OptionOrNullable<TFrom>, Option<TTo>>;\nexport function getOptionCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: FixedSizeCodec<TFrom, TTo>,\n    config: OptionCodecConfig<NumberCodec> & { noneValue: 'zeroes' },\n): VariableSizeCodec<OptionOrNullable<TFrom>, Option<TTo>>;\nexport function getOptionCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config?: OptionCodecConfig<NumberCodec> & { noneValue?: ReadonlyUint8Array },\n): VariableSizeCodec<OptionOrNullable<TFrom>, Option<TTo>>;\nexport function getOptionCodec<TFrom, TTo extends TFrom = TFrom>(\n    item: Codec<TFrom, TTo>,\n    config: OptionCodecConfig<NumberCodec> = {},\n): Codec<OptionOrNullable<TFrom>, Option<TTo>> {\n    type ConfigCast = OptionCodecConfig<NumberCodec> & { noneValue?: ReadonlyUint8Array };\n    return combineCodec(\n        getOptionEncoder<TFrom>(item, config as ConfigCast),\n        getOptionDecoder<TTo>(item, config as ConfigCast),\n    );\n}\n", "import { isOption, isSome, None, Some } from './option';\n\n/**\n * Lists all types that should not be recursively unwrapped.\n *\n * @see {@link UnwrappedOption}\n */\ntype UnUnwrappables =\n    | Date\n    | Int8Array\n    | Int16Array\n    | Int32Array\n    | Uint8Array\n    | Uint16Array\n    | Uint32Array\n    | bigint\n    | boolean\n    | number\n    | string\n    | symbol\n    | null\n    | undefined;\n\n/**\n * A type that defines the recursive unwrapping of a type `T`\n * such that all nested {@link Option} types are unwrapped.\n *\n * For each nested {@link Option} type, if the option is a {@link Some},\n * it returns the type of its value, otherwise, it returns the provided\n * fallback type `U` which defaults to `null`.\n */\nexport type UnwrappedOption<T, U = null> =\n    T extends Some<infer TValue>\n        ? UnwrappedOption<TValue, U>\n        : T extends None\n          ? U\n          : T extends UnUnwrappables\n            ? T\n            : T extends object\n              ? { [key in keyof T]: UnwrappedOption<T[key], U> }\n              : T extends Array<infer TItem>\n                ? Array<UnwrappedOption<TItem, U>>\n                : T;\n\n/**\n * Recursively go through a type `T` such that all\n * nested {@link Option} types are unwrapped.\n *\n * For each nested {@link Option} type, if the option is a {@link Some},\n * it returns its value, otherwise, it returns the provided fallback value\n * which defaults to `null`.\n */\nexport function unwrapOptionRecursively<T>(input: T): UnwrappedOption<T>;\nexport function unwrapOptionRecursively<T, U>(input: T, fallback: () => U): UnwrappedOption<T, U>;\nexport function unwrapOptionRecursively<T, U = null>(input: T, fallback?: () => U): UnwrappedOption<T, U> {\n    // Types to bypass.\n    if (!input || ArrayBuffer.isView(input)) {\n        return input as UnwrappedOption<T, U>;\n    }\n\n    const next = <X>(x: X) =>\n        (fallback ? unwrapOptionRecursively(x, fallback) : unwrapOptionRecursively(x)) as UnwrappedOption<X, U>;\n\n    // Handle Option.\n    if (isOption(input)) {\n        if (isSome(input)) return next(input.value) as UnwrappedOption<T, U>;\n        return (fallback ? fallback() : null) as UnwrappedOption<T, U>;\n    }\n\n    // Walk.\n    if (Array.isArray(input)) {\n        return input.map(next) as UnwrappedOption<T, U>;\n    }\n    if (typeof input === 'object') {\n        return Object.fromEntries(Object.entries(input).map(([k, v]) => [k, next(v)])) as UnwrappedOption<T, U>;\n    }\n    return input as UnwrappedOption<T, U>;\n}\n"]}