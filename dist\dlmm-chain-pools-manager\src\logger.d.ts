/**
 * DLMM串联池流动性管理脚本 - 日志模块
 *
 * 负责系统日志管理，支持多级别日志和格式化输出
 */
export declare enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3
}
/**
 * 日志管理器类
 */
declare class Logger {
    private level;
    private useTimestamp;
    private logToFile;
    private logFilePath;
    private isDebugMode;
    constructor();
    /**
     * 获取当前时间戳字符串
     */
    private getTimestamp;
    /**
     * 格式化日志消息
     */
    private formatMessage;
    /**
     * 写入日志到文件
     */
    private writeToFile;
    /**
     * DEBUG级别日志
     */
    debug(message: string, ...args: any[]): void;
    /**
     * INFO级别日志
     */
    info(message: string, ...args: any[]): void;
    /**
     * WARN级别日志
     */
    warn(message: string, ...args: any[]): void;
    /**
     * ERROR级别日志
     */
    error(message: string, ...args: any[]): void;
    /**
     * 记录错误对象
     */
    logError(error: Error, context?: string): void;
    /**
     * 分组标记，用于视觉上分隔日志消息
     */
    group(title: string): void;
}
export declare const logger: Logger;
export {};
