import { Keypair } from '@solana/web3.js';
import * as bs58 from 'bs58';

/**
 * 生成新的 Solana 钱包
 */
function generateWallet() {
  console.log('========================================');
  console.log('Solana 钱包生成器');
  console.log('========================================');
  
  // 生成新的密钥对
  const keypair = Keypair.generate();
  
  // 获取公钥（钱包地址）
  const publicKey = keypair.publicKey.toString();
  
  // 获取私钥（Base58 格式）
  const privateKey = bs58.encode(keypair.secretKey);
  
  console.log('\n✅ 新钱包生成成功！');
  console.log('\n📍 钱包地址（公钥）:');
  console.log(publicKey);
  console.log('\n🔑 私钥（请妥善保管）:');
  console.log(privateKey);
  
  console.log('\n⚠️  重要提示:');
  console.log('1. 请将私钥保存在安全的地方');
  console.log('2. 不要与任何人分享您的私钥');
  console.log('3. 这是一个新钱包，余额为 0 SOL');
  console.log('4. 如需使用，请先向此地址转入一些 SOL');
  
  console.log('\n📋 接下来的步骤:');
  console.log('1. 复制上面的私钥');
  console.log('2. 运行: npm run encrypt-key');
  console.log('3. 粘贴私钥并设置密码');
  console.log('4. 启动项目: npm start');
  
  console.log('\n========================================');
}

// 运行生成器
generateWallet();
