{"version": 3, "file": "state.js", "sourceRoot": "", "sources": ["../../../../src/extensions/transferFee/state.ts"], "names": [], "mappings": ";;;AAuCA,8CAEC;AAGD,oCAUC;AAcD,kCAMC;AAGD,8CAGC;AAWD,oDAOC;AAED,oDAOC;AA1GD,yDAAoD;AACpD,qEAA6D;AAI7D,0DAAsE;AAEzD,QAAA,oBAAoB,GAAG,KAAK,CAAC;AAC7B,QAAA,mBAAmB,GAAG,MAAM,CAAC,4BAAoB,CAAC,CAAC;AA6BhE,sDAAsD;AACtD,SAAgB,iBAAiB,CAAC,QAAiB;IAC/C,OAAO,IAAA,sBAAM,EAAc,CAAC,IAAA,yBAAG,EAAC,OAAO,CAAC,EAAE,IAAA,yBAAG,EAAC,YAAY,CAAC,EAAE,IAAA,mBAAG,EAAC,wBAAwB,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;AAC3G,CAAC;AAED,iCAAiC;AACjC,SAAgB,YAAY,CAAC,WAAwB,EAAE,YAAoB;IACvE,MAAM,sBAAsB,GAAG,WAAW,CAAC,sBAAsB,CAAC;IAClE,IAAI,sBAAsB,KAAK,CAAC,IAAI,YAAY,KAAK,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;QAC7D,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC;SAAM,CAAC;QACJ,MAAM,SAAS,GAAG,YAAY,GAAG,MAAM,CAAC,sBAAsB,CAAC,CAAC;QAChE,MAAM,MAAM,GAAG,CAAC,SAAS,GAAG,2BAAmB,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,2BAAmB,CAAC;QACnF,MAAM,GAAG,GAAG,MAAM,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC;QAC9E,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;IACvB,CAAC;AACL,CAAC;AAED,uEAAuE;AAC1D,QAAA,uBAAuB,GAAG,IAAA,sBAAM,EAAoB;IAC7D,IAAA,+BAAS,EAAC,4BAA4B,CAAC;IACvC,IAAA,+BAAS,EAAC,2BAA2B,CAAC;IACtC,IAAA,yBAAG,EAAC,gBAAgB,CAAC;IACrB,iBAAiB,CAAC,kBAAkB,CAAC;IACrC,iBAAiB,CAAC,kBAAkB,CAAC;CACxC,CAAC,CAAC;AAEU,QAAA,wBAAwB,GAAG,+BAAuB,CAAC,IAAI,CAAC;AAErE,kCAAkC;AAClC,SAAgB,WAAW,CAAC,iBAAoC,EAAE,KAAa;IAC3E,IAAI,KAAK,IAAI,iBAAiB,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QACpD,OAAO,iBAAiB,CAAC,gBAAgB,CAAC;IAC9C,CAAC;SAAM,CAAC;QACJ,OAAO,iBAAiB,CAAC,gBAAgB,CAAC;IAC9C,CAAC;AACL,CAAC;AAED,6DAA6D;AAC7D,SAAgB,iBAAiB,CAAC,iBAAoC,EAAE,KAAa,EAAE,YAAoB;IACvG,MAAM,WAAW,GAAG,WAAW,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;IAC1D,OAAO,YAAY,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;AACnD,CAAC;AAOD,uCAAuC;AAC1B,QAAA,uBAAuB,GAAG,IAAA,sBAAM,EAAoB,CAAC,IAAA,yBAAG,EAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAC7E,QAAA,wBAAwB,GAAG,+BAAuB,CAAC,IAAI,CAAC;AAErE,SAAgB,oBAAoB,CAAC,IAAU;IAC3C,MAAM,aAAa,GAAG,IAAA,mCAAgB,EAAC,gCAAa,CAAC,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IACtF,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;QACzB,OAAO,+BAAuB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IACzD,CAAC;SAAM,CAAC;QACJ,OAAO,IAAI,CAAC;IAChB,CAAC;AACL,CAAC;AAED,SAAgB,oBAAoB,CAAC,OAAgB;IACjD,MAAM,aAAa,GAAG,IAAA,mCAAgB,EAAC,gCAAa,CAAC,iBAAiB,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;IACzF,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;QACzB,OAAO,+BAAuB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IACzD,CAAC;SAAM,CAAC;QACJ,OAAO,IAAI,CAAC;IAChB,CAAC;AACL,CAAC"}