{"version": 3, "file": "option.d.ts", "sourceRoot": "", "sources": ["../../src/option.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AACH,MAAM,MAAM,MAAM,CAAC,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAEvC;;;;GAIG;AACH,MAAM,MAAM,gBAAgB,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AAEvD;;;;GAIG;AACH,MAAM,MAAM,IAAI,CAAC,CAAC,IAAI,QAAQ,CAAC;IAAE,QAAQ,EAAE,MAAM,CAAC;IAAC,KAAK,EAAE,CAAC,CAAA;CAAE,CAAC,CAAC;AAE/D;;;;GAIG;AACH,MAAM,MAAM,IAAI,GAAG,QAAQ,CAAC;IAAE,QAAQ,EAAE,MAAM,CAAA;CAAE,CAAC,CAAC;AAElD;;;;GAIG;AACH,eAAO,MAAM,IAAI,GAAI,CAAC,SAAS,CAAC,KAAG,MAAM,CAAC,CAAC,CAAkC,CAAC;AAE9E;;;;GAIG;AACH,eAAO,MAAM,IAAI,GAAI,CAAC,OAAK,MAAM,CAAC,CAAC,CAA2B,CAAC;AAE/D;;GAEG;AACH,eAAO,MAAM,QAAQ,GAAI,CAAC,mBAAmB,OAAO,KAAG,KAAK,IAAI,MAAM,CAAC,CAAC,CAMnE,CAAC;AAEN;;GAEG;AACH,eAAO,MAAM,MAAM,GAAI,CAAC,UAAU,MAAM,CAAC,CAAC,CAAC,KAAG,MAAM,IAAI,IAAI,CAAC,CAAC,CAA+B,CAAC;AAE9F;;GAEG;AACH,eAAO,MAAM,MAAM,GAAI,CAAC,UAAU,MAAM,CAAC,CAAC,CAAC,KAAG,MAAM,IAAI,IAAkC,CAAC"}