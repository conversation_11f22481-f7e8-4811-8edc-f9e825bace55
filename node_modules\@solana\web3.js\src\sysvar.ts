import {PublicKey} from './publickey';

export const SY<PERSON><PERSON>_CLOCK_PUBKEY = new PublicKey(
  'SysvarC1ock11111111111111111111111111111111',
);

export const SYSVAR_EPOCH_SCHEDULE_PUBKEY = new PublicKey(
  'SysvarEpochSchedu1e111111111111111111111111',
);

export const SYSVAR_INSTRUCTIONS_PUBKEY = new PublicKey(
  'Sysvar1nstructions1111111111111111111111111',
);

export const SYSVAR_RECENT_BLOCKHASHES_PUBKEY = new PublicKey(
  'SysvarRecentB1ockHashes11111111111111111111',
);

export const SYSVAR_RENT_PUBKEY = new PublicKey(
  'SysvarRent111111111111111111111111111111111',
);

export const SYSVAR_REWARDS_PUBKEY = new PublicKey(
  'SysvarRewards111111111111111111111111111111',
);

export const SY<PERSON><PERSON>_SLOT_HASHES_PUBKEY = new PublicKey(
  'SysvarS1otHashes111111111111111111111111111',
);

export const SYSVAR_SLOT_HISTORY_PUBKEY = new PublicKey(
  'SysvarS1otHistory11111111111111111111111111',
);

export const SYSVAR_STAKE_HISTORY_PUBKEY = new PublicKey(
  'SysvarStakeHistory1111111111111111111111111',
);
